{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\portfolio-react\\\\src\\\\components\\\\Header.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"logo\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/logo.PNG\",\n          alt: \"Logo\",\n          className: \"logo-img\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n      href: \"https://www.canva.com/design/DAGNjKc0Cr0/nJ-kiMZTpFhVUD6B6nyPYg/view?utm_content=DAGNjKc0Cr0&utm_campaign=designshare&utm_medium=link2&utm_source=uniquelinks&utlId=h6802188a93\",\n      className: \"cv-button\",\n      target: \"_blank\",\n      rel: \"noopener noreferrer\",\n      children: \"Get CV\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Header", "children", "className", "to", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "target", "rel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/components/Header.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Header = () => {\n  return (\n    <header>\n      <div className=\"logo\">\n        <Link to=\"/\">\n          <img src=\"/logo.PNG\" alt=\"Logo\" className=\"logo-img\" />\n        </Link>\n      </div>\n      <a \n        href=\"https://www.canva.com/design/DAGNjKc0Cr0/nJ-kiMZTpFhVUD6B6nyPYg/view?utm_content=DAGNjKc0Cr0&utm_campaign=designshare&utm_medium=link2&utm_source=uniquelinks&utlId=h6802188a93\" \n        className=\"cv-button\"\n        target=\"_blank\"\n        rel=\"noopener noreferrer\"\n      >\n        Get CV\n      </a>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACED,OAAA;IAAAE,QAAA,gBACEF,OAAA;MAAKG,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBF,OAAA,CAACF,IAAI;QAACM,EAAE,EAAC,GAAG;QAAAF,QAAA,eACVF,OAAA;UAAKK,GAAG,EAAC,WAAW;UAACC,GAAG,EAAC,MAAM;UAACH,SAAS,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNV,OAAA;MACEW,IAAI,EAAC,iLAAiL;MACtLR,SAAS,EAAC,WAAW;MACrBS,MAAM,EAAC,QAAQ;MACfC,GAAG,EAAC,qBAAqB;MAAAX,QAAA,EAC1B;IAED;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEb,CAAC;AAACI,EAAA,GAlBIb,MAAM;AAoBZ,eAAeA,MAAM;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}