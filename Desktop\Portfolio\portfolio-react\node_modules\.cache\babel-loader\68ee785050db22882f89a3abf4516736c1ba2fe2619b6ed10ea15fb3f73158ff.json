{"ast": null, "code": "import { g as getDocument, a as getWindow } from '../shared/ssr-window.esm.mjs';\nimport { e as elementChildren } from '../shared/utils.mjs';\nfunction HashNavigation(_ref) {\n  let {\n    swiper,\n    extendParams,\n    emit,\n    on\n  } = _ref;\n  let initialized = false;\n  const document = getDocument();\n  const window = getWindow();\n  extendParams({\n    hashNavigation: {\n      enabled: false,\n      replaceState: false,\n      watchState: false,\n      getSlideIndex(_s, hash) {\n        if (swiper.virtual && swiper.params.virtual.enabled) {\n          const slideWithHash = swiper.slides.find(slideEl => slideEl.getAttribute('data-hash') === hash);\n          if (!slideWithHash) return 0;\n          const index = parseInt(slideWithHash.getAttribute('data-swiper-slide-index'), 10);\n          return index;\n        }\n        return swiper.getSlideIndex(elementChildren(swiper.slidesEl, `.${swiper.params.slideClass}[data-hash=\"${hash}\"], swiper-slide[data-hash=\"${hash}\"]`)[0]);\n      }\n    }\n  });\n  const onHashChange = () => {\n    emit('hashChange');\n    const newHash = document.location.hash.replace('#', '');\n    const activeSlideEl = swiper.virtual && swiper.params.virtual.enabled ? swiper.slidesEl.querySelector(`[data-swiper-slide-index=\"${swiper.activeIndex}\"]`) : swiper.slides[swiper.activeIndex];\n    const activeSlideHash = activeSlideEl ? activeSlideEl.getAttribute('data-hash') : '';\n    if (newHash !== activeSlideHash) {\n      const newIndex = swiper.params.hashNavigation.getSlideIndex(swiper, newHash);\n      if (typeof newIndex === 'undefined' || Number.isNaN(newIndex)) return;\n      swiper.slideTo(newIndex);\n    }\n  };\n  const setHash = () => {\n    if (!initialized || !swiper.params.hashNavigation.enabled) return;\n    const activeSlideEl = swiper.virtual && swiper.params.virtual.enabled ? swiper.slidesEl.querySelector(`[data-swiper-slide-index=\"${swiper.activeIndex}\"]`) : swiper.slides[swiper.activeIndex];\n    const activeSlideHash = activeSlideEl ? activeSlideEl.getAttribute('data-hash') || activeSlideEl.getAttribute('data-history') : '';\n    if (swiper.params.hashNavigation.replaceState && window.history && window.history.replaceState) {\n      window.history.replaceState(null, null, `#${activeSlideHash}` || '');\n      emit('hashSet');\n    } else {\n      document.location.hash = activeSlideHash || '';\n      emit('hashSet');\n    }\n  };\n  const init = () => {\n    if (!swiper.params.hashNavigation.enabled || swiper.params.history && swiper.params.history.enabled) return;\n    initialized = true;\n    const hash = document.location.hash.replace('#', '');\n    if (hash) {\n      const speed = 0;\n      const index = swiper.params.hashNavigation.getSlideIndex(swiper, hash);\n      swiper.slideTo(index || 0, speed, swiper.params.runCallbacksOnInit, true);\n    }\n    if (swiper.params.hashNavigation.watchState) {\n      window.addEventListener('hashchange', onHashChange);\n    }\n  };\n  const destroy = () => {\n    if (swiper.params.hashNavigation.watchState) {\n      window.removeEventListener('hashchange', onHashChange);\n    }\n  };\n  on('init', () => {\n    if (swiper.params.hashNavigation.enabled) {\n      init();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.params.hashNavigation.enabled) {\n      destroy();\n    }\n  });\n  on('transitionEnd _freeModeNoMomentumRelease', () => {\n    if (initialized) {\n      setHash();\n    }\n  });\n  on('slideChange', () => {\n    if (initialized && swiper.params.cssMode) {\n      setHash();\n    }\n  });\n}\nexport { HashNavigation as default };", "map": {"version": 3, "names": ["g", "getDocument", "a", "getWindow", "e", "elementChildren", "HashNavigation", "_ref", "swiper", "extendParams", "emit", "on", "initialized", "document", "window", "hashNavigation", "enabled", "replaceState", "watchState", "getSlideIndex", "_s", "hash", "virtual", "params", "slideWithHash", "slides", "find", "slideEl", "getAttribute", "index", "parseInt", "slidesEl", "slideClass", "onHashChange", "newHash", "location", "replace", "activeSlideEl", "querySelector", "activeIndex", "activeSlideHash", "newIndex", "Number", "isNaN", "slideTo", "setHash", "history", "init", "speed", "runCallbacksOnInit", "addEventListener", "destroy", "removeEventListener", "cssMode", "default"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/node_modules/swiper/modules/hash-navigation.mjs"], "sourcesContent": ["import { g as getDocument, a as getWindow } from '../shared/ssr-window.esm.mjs';\nimport { e as elementChildren } from '../shared/utils.mjs';\n\nfunction HashNavigation(_ref) {\n  let {\n    swiper,\n    extendParams,\n    emit,\n    on\n  } = _ref;\n  let initialized = false;\n  const document = getDocument();\n  const window = getWindow();\n  extendParams({\n    hashNavigation: {\n      enabled: false,\n      replaceState: false,\n      watchState: false,\n      getSlideIndex(_s, hash) {\n        if (swiper.virtual && swiper.params.virtual.enabled) {\n          const slideWithHash = swiper.slides.find(slideEl => slideEl.getAttribute('data-hash') === hash);\n          if (!slideWithHash) return 0;\n          const index = parseInt(slideWithHash.getAttribute('data-swiper-slide-index'), 10);\n          return index;\n        }\n        return swiper.getSlideIndex(elementChildren(swiper.slidesEl, `.${swiper.params.slideClass}[data-hash=\"${hash}\"], swiper-slide[data-hash=\"${hash}\"]`)[0]);\n      }\n    }\n  });\n  const onHashChange = () => {\n    emit('hashChange');\n    const newHash = document.location.hash.replace('#', '');\n    const activeSlideEl = swiper.virtual && swiper.params.virtual.enabled ? swiper.slidesEl.querySelector(`[data-swiper-slide-index=\"${swiper.activeIndex}\"]`) : swiper.slides[swiper.activeIndex];\n    const activeSlideHash = activeSlideEl ? activeSlideEl.getAttribute('data-hash') : '';\n    if (newHash !== activeSlideHash) {\n      const newIndex = swiper.params.hashNavigation.getSlideIndex(swiper, newHash);\n      if (typeof newIndex === 'undefined' || Number.isNaN(newIndex)) return;\n      swiper.slideTo(newIndex);\n    }\n  };\n  const setHash = () => {\n    if (!initialized || !swiper.params.hashNavigation.enabled) return;\n    const activeSlideEl = swiper.virtual && swiper.params.virtual.enabled ? swiper.slidesEl.querySelector(`[data-swiper-slide-index=\"${swiper.activeIndex}\"]`) : swiper.slides[swiper.activeIndex];\n    const activeSlideHash = activeSlideEl ? activeSlideEl.getAttribute('data-hash') || activeSlideEl.getAttribute('data-history') : '';\n    if (swiper.params.hashNavigation.replaceState && window.history && window.history.replaceState) {\n      window.history.replaceState(null, null, `#${activeSlideHash}` || '');\n      emit('hashSet');\n    } else {\n      document.location.hash = activeSlideHash || '';\n      emit('hashSet');\n    }\n  };\n  const init = () => {\n    if (!swiper.params.hashNavigation.enabled || swiper.params.history && swiper.params.history.enabled) return;\n    initialized = true;\n    const hash = document.location.hash.replace('#', '');\n    if (hash) {\n      const speed = 0;\n      const index = swiper.params.hashNavigation.getSlideIndex(swiper, hash);\n      swiper.slideTo(index || 0, speed, swiper.params.runCallbacksOnInit, true);\n    }\n    if (swiper.params.hashNavigation.watchState) {\n      window.addEventListener('hashchange', onHashChange);\n    }\n  };\n  const destroy = () => {\n    if (swiper.params.hashNavigation.watchState) {\n      window.removeEventListener('hashchange', onHashChange);\n    }\n  };\n  on('init', () => {\n    if (swiper.params.hashNavigation.enabled) {\n      init();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.params.hashNavigation.enabled) {\n      destroy();\n    }\n  });\n  on('transitionEnd _freeModeNoMomentumRelease', () => {\n    if (initialized) {\n      setHash();\n    }\n  });\n  on('slideChange', () => {\n    if (initialized && swiper.params.cssMode) {\n      setHash();\n    }\n  });\n}\n\nexport { HashNavigation as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,SAAS,QAAQ,8BAA8B;AAC/E,SAASC,CAAC,IAAIC,eAAe,QAAQ,qBAAqB;AAE1D,SAASC,cAAcA,CAACC,IAAI,EAAE;EAC5B,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC,IAAI;IACJC;EACF,CAAC,GAAGJ,IAAI;EACR,IAAIK,WAAW,GAAG,KAAK;EACvB,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAMa,MAAM,GAAGX,SAAS,CAAC,CAAC;EAC1BM,YAAY,CAAC;IACXM,cAAc,EAAE;MACdC,OAAO,EAAE,KAAK;MACdC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,KAAK;MACjBC,aAAaA,CAACC,EAAE,EAAEC,IAAI,EAAE;QACtB,IAAIb,MAAM,CAACc,OAAO,IAAId,MAAM,CAACe,MAAM,CAACD,OAAO,CAACN,OAAO,EAAE;UACnD,MAAMQ,aAAa,GAAGhB,MAAM,CAACiB,MAAM,CAACC,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACC,YAAY,CAAC,WAAW,CAAC,KAAKP,IAAI,CAAC;UAC/F,IAAI,CAACG,aAAa,EAAE,OAAO,CAAC;UAC5B,MAAMK,KAAK,GAAGC,QAAQ,CAACN,aAAa,CAACI,YAAY,CAAC,yBAAyB,CAAC,EAAE,EAAE,CAAC;UACjF,OAAOC,KAAK;QACd;QACA,OAAOrB,MAAM,CAACW,aAAa,CAACd,eAAe,CAACG,MAAM,CAACuB,QAAQ,EAAE,IAAIvB,MAAM,CAACe,MAAM,CAACS,UAAU,eAAeX,IAAI,+BAA+BA,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1J;IACF;EACF,CAAC,CAAC;EACF,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzBvB,IAAI,CAAC,YAAY,CAAC;IAClB,MAAMwB,OAAO,GAAGrB,QAAQ,CAACsB,QAAQ,CAACd,IAAI,CAACe,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;IACvD,MAAMC,aAAa,GAAG7B,MAAM,CAACc,OAAO,IAAId,MAAM,CAACe,MAAM,CAACD,OAAO,CAACN,OAAO,GAAGR,MAAM,CAACuB,QAAQ,CAACO,aAAa,CAAC,6BAA6B9B,MAAM,CAAC+B,WAAW,IAAI,CAAC,GAAG/B,MAAM,CAACiB,MAAM,CAACjB,MAAM,CAAC+B,WAAW,CAAC;IAC9L,MAAMC,eAAe,GAAGH,aAAa,GAAGA,aAAa,CAACT,YAAY,CAAC,WAAW,CAAC,GAAG,EAAE;IACpF,IAAIM,OAAO,KAAKM,eAAe,EAAE;MAC/B,MAAMC,QAAQ,GAAGjC,MAAM,CAACe,MAAM,CAACR,cAAc,CAACI,aAAa,CAACX,MAAM,EAAE0B,OAAO,CAAC;MAC5E,IAAI,OAAOO,QAAQ,KAAK,WAAW,IAAIC,MAAM,CAACC,KAAK,CAACF,QAAQ,CAAC,EAAE;MAC/DjC,MAAM,CAACoC,OAAO,CAACH,QAAQ,CAAC;IAC1B;EACF,CAAC;EACD,MAAMI,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAI,CAACjC,WAAW,IAAI,CAACJ,MAAM,CAACe,MAAM,CAACR,cAAc,CAACC,OAAO,EAAE;IAC3D,MAAMqB,aAAa,GAAG7B,MAAM,CAACc,OAAO,IAAId,MAAM,CAACe,MAAM,CAACD,OAAO,CAACN,OAAO,GAAGR,MAAM,CAACuB,QAAQ,CAACO,aAAa,CAAC,6BAA6B9B,MAAM,CAAC+B,WAAW,IAAI,CAAC,GAAG/B,MAAM,CAACiB,MAAM,CAACjB,MAAM,CAAC+B,WAAW,CAAC;IAC9L,MAAMC,eAAe,GAAGH,aAAa,GAAGA,aAAa,CAACT,YAAY,CAAC,WAAW,CAAC,IAAIS,aAAa,CAACT,YAAY,CAAC,cAAc,CAAC,GAAG,EAAE;IAClI,IAAIpB,MAAM,CAACe,MAAM,CAACR,cAAc,CAACE,YAAY,IAAIH,MAAM,CAACgC,OAAO,IAAIhC,MAAM,CAACgC,OAAO,CAAC7B,YAAY,EAAE;MAC9FH,MAAM,CAACgC,OAAO,CAAC7B,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,IAAIuB,eAAe,EAAE,IAAI,EAAE,CAAC;MACpE9B,IAAI,CAAC,SAAS,CAAC;IACjB,CAAC,MAAM;MACLG,QAAQ,CAACsB,QAAQ,CAACd,IAAI,GAAGmB,eAAe,IAAI,EAAE;MAC9C9B,IAAI,CAAC,SAAS,CAAC;IACjB;EACF,CAAC;EACD,MAAMqC,IAAI,GAAGA,CAAA,KAAM;IACjB,IAAI,CAACvC,MAAM,CAACe,MAAM,CAACR,cAAc,CAACC,OAAO,IAAIR,MAAM,CAACe,MAAM,CAACuB,OAAO,IAAItC,MAAM,CAACe,MAAM,CAACuB,OAAO,CAAC9B,OAAO,EAAE;IACrGJ,WAAW,GAAG,IAAI;IAClB,MAAMS,IAAI,GAAGR,QAAQ,CAACsB,QAAQ,CAACd,IAAI,CAACe,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;IACpD,IAAIf,IAAI,EAAE;MACR,MAAM2B,KAAK,GAAG,CAAC;MACf,MAAMnB,KAAK,GAAGrB,MAAM,CAACe,MAAM,CAACR,cAAc,CAACI,aAAa,CAACX,MAAM,EAAEa,IAAI,CAAC;MACtEb,MAAM,CAACoC,OAAO,CAACf,KAAK,IAAI,CAAC,EAAEmB,KAAK,EAAExC,MAAM,CAACe,MAAM,CAAC0B,kBAAkB,EAAE,IAAI,CAAC;IAC3E;IACA,IAAIzC,MAAM,CAACe,MAAM,CAACR,cAAc,CAACG,UAAU,EAAE;MAC3CJ,MAAM,CAACoC,gBAAgB,CAAC,YAAY,EAAEjB,YAAY,CAAC;IACrD;EACF,CAAC;EACD,MAAMkB,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAI3C,MAAM,CAACe,MAAM,CAACR,cAAc,CAACG,UAAU,EAAE;MAC3CJ,MAAM,CAACsC,mBAAmB,CAAC,YAAY,EAAEnB,YAAY,CAAC;IACxD;EACF,CAAC;EACDtB,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAIH,MAAM,CAACe,MAAM,CAACR,cAAc,CAACC,OAAO,EAAE;MACxC+B,IAAI,CAAC,CAAC;IACR;EACF,CAAC,CAAC;EACFpC,EAAE,CAAC,SAAS,EAAE,MAAM;IAClB,IAAIH,MAAM,CAACe,MAAM,CAACR,cAAc,CAACC,OAAO,EAAE;MACxCmC,OAAO,CAAC,CAAC;IACX;EACF,CAAC,CAAC;EACFxC,EAAE,CAAC,0CAA0C,EAAE,MAAM;IACnD,IAAIC,WAAW,EAAE;MACfiC,OAAO,CAAC,CAAC;IACX;EACF,CAAC,CAAC;EACFlC,EAAE,CAAC,aAAa,EAAE,MAAM;IACtB,IAAIC,WAAW,IAAIJ,MAAM,CAACe,MAAM,CAAC8B,OAAO,EAAE;MACxCR,OAAO,CAAC,CAAC;IACX;EACF,CAAC,CAAC;AACJ;AAEA,SAASvC,cAAc,IAAIgD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}