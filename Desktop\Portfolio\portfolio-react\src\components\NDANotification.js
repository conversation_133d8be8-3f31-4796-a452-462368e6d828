import React, { useEffect } from 'react';
import './NDANotification.css';

const NDANotification = ({ isOpen, onClose, projectTitle }) => {
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="nda-overlay" onClick={onClose}>
      <div className="nda-content" onClick={(e) => e.stopPropagation()}>
        <div className="nda-icon">
          <span>🔒</span>
        </div>
        <h2 className="nda-title">Project Under NDA</h2>
        <p className="nda-message">
          This project is protected under a Non-Disclosure Agreement (NDA). 
          Due to confidentiality requirements, detailed information and live demos cannot be shared publicly.
        </p>
        <div className="nda-details">
          <h3>What I can share:</h3>
          <ul>
            <li>General technical scope and technologies used</li>
            <li>My role and contributions to the project</li>
            <li>Skills and methodologies applied</li>
            <li>Overall project outcomes and achievements</li>
          </ul>
        </div>
        <div className="nda-contact">
          <p>For more information about this project, please contact me directly.</p>
        </div>
        <button className="nda-close-btn" onClick={onClose}>
          <span>Understood</span>
        </button>
      </div>
    </div>
  );
};

export default NDANotification;
