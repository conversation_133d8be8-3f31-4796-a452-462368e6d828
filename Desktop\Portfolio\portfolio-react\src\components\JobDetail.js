import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { jobsData } from '../data/jobsData';
import Header from './Header';
import Footer from './Footer';
import ProjectImageSwiper from './ProjectImageSwiper';
import NDANotification from './NDANotification';
import '../job-detail.css';
import './ProjectImageSwiper.css';

const JobDetail = () => {
  const { slug } = useParams();
  const job = jobsData.find(job => job.slug === slug);
  const [ndaNotification, setNdaNotification] = useState({ isOpen: false, projectTitle: '' });

  // Function to check if a project is NDA protected
  const isNDAProject = (project) => {
    return project.description.toLowerCase().includes('nda') ||
           project.title.toLowerCase().includes('nda') ||
           project.images.some(img => img.includes('NDA'));
  };

  const handleProjectInfoClick = (e, project) => {
    e.stopPropagation(); // Prevent any parent click handlers

    // Check if project is NDA protected
    if (isNDAProject(project)) {
      setNdaNotification({ isOpen: true, projectTitle: project.title });
      return;
    }

    window.open(project.liveUrl, '_blank');
  };

  const closeNdaNotification = () => {
    setNdaNotification({ isOpen: false, projectTitle: '' });
  };

  if (!job) {
    return (
      <div>
        <Header />
        <div style={{ padding: '100px 20px', textAlign: 'center', color: 'white' }}>
          <h1>Job Not Found</h1>
          <Link to="/" style={{ color: '#4B0082' }}>← Back to Home</Link>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div>
      <Header />
      
      {/* Navigation Back */}
      <div className="back-navigation">
        <Link to="/#experience" className="back-button">
          <span className="back-arrow">←</span>
          <span>Back to Timeline</span>
        </Link>
      </div>

      {/* Job Detail Hero Section */}
      <section className="job-hero">
        <div className="job-hero-content">
          <div className="company-branding">
            <img 
              src={job.logo} 
              alt={job.logoAlt} 
              className="hero-company-logo" 
            />
            <div className="company-info">
              <h1 className="job-title-hero">{job.title}</h1>
              <h2 className="company-name-hero">{job.company}</h2>
              {job.companyLink && (
                <p className="company-link-hero">
                  <a href={job.companyLink} target="_blank" rel="noopener noreferrer">
                    {job.companyLink}
                  </a>
                </p>
              )}
              <p className="job-duration-hero">{job.duration}</p>
            </div>
          </div>
          <div className="job-summary">
            <p>{job.summary}</p>
          </div>
        </div>
      </section>

      {/* Job Details Content */}
      <section className="job-content">
        <div className="content-grid">
          {/* Full Job Description */}
          <div className="content-card">
            <h3>Role Overview</h3>
            <p>{job.roleOverview}</p>
            
            <h4>Key Responsibilities</h4>
            <ul>
              {job.responsibilities.map((responsibility, index) => (
                <li key={index}>{responsibility}</li>
              ))}
            </ul>
          </div>

          {/* Skills & Technologies */}
          <div className="content-card">
            <h3>Technologies & Skills</h3>
            <div className="skills-grid">
              {Object.entries(job.skills).map(([category, skills]) => {
                // Generate class name based on category name
                const categoryClass = category.toLowerCase().replace(/[^a-z0-9]/g, '_') + '_skills';
                return (
                  <div key={category} className={`skill-category ${categoryClass}`}>
                    <h4>{category}</h4>
                    <div className="skill-tags">
                      {skills.map((skill, index) => (
                        <span key={index} className="skill-tag">{skill}</span>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Key Accomplishments */}
          <div className="content-card">
            <h3>Key Accomplishments</h3>
            <div className="accomplishments-list">
              {job.accomplishments.map((accomplishment, index) => (
                <div key={index} className="accomplishment-item">
                  <div className="metric">{accomplishment.metric}</div>
                  <div className="metric-description">{accomplishment.description}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Project Portfolio from this role */}
      <section className="role-projects">
        <h2>Projects from this Role</h2>
        <div className="projects-grid">
          {job.projects.map((project, index) => (
            <div
              key={index}
              className="project-card"
            >
              <div className="project-image">
                <ProjectImageSwiper
                  images={project.images}
                  title={project.title}
                  isNDA={isNDAProject(project)}
                />
              </div>
              <div
                className="project-info"
                onClick={(e) => handleProjectInfoClick(e, project)}
                style={{ cursor: 'pointer' }}
              >
                <h3>{project.title}</h3>
                <p>{project.description}</p>
                <div className="project-tech">
                  {project.technologies.map((tech, techIndex) => (
                    <span key={techIndex}>{tech}</span>
                  ))}
                </div>
                {project.liveUrl && (
                  <div className="project-link">
                    <span>
                      {isNDAProject(project) ? 'Click to view NDA info →' : 'Click to view project →'}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* NDA Notification Modal */}
      <NDANotification
        isOpen={ndaNotification.isOpen}
        onClose={closeNdaNotification}
        projectTitle={ndaNotification.projectTitle}
      />

      <Footer />
    </div>
  );
};

export default JobDetail;
