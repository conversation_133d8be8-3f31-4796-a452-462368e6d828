{"ast": null, "code": "import { a as getWindow } from '../shared/ssr-window.esm.mjs';\nfunction History(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    history: {\n      enabled: false,\n      root: '',\n      replaceState: false,\n      key: 'slides',\n      keepQuery: false\n    }\n  });\n  let initialized = false;\n  let paths = {};\n  const slugify = text => {\n    return text.toString().replace(/\\s+/g, '-').replace(/[^\\w-]+/g, '').replace(/--+/g, '-').replace(/^-+/, '').replace(/-+$/, '');\n  };\n  const getPathValues = urlOverride => {\n    const window = getWindow();\n    let location;\n    if (urlOverride) {\n      location = new URL(urlOverride);\n    } else {\n      location = window.location;\n    }\n    const pathArray = location.pathname.slice(1).split('/').filter(part => part !== '');\n    const total = pathArray.length;\n    const key = pathArray[total - 2];\n    const value = pathArray[total - 1];\n    return {\n      key,\n      value\n    };\n  };\n  const setHistory = (key, index) => {\n    const window = getWindow();\n    if (!initialized || !swiper.params.history.enabled) return;\n    let location;\n    if (swiper.params.url) {\n      location = new URL(swiper.params.url);\n    } else {\n      location = window.location;\n    }\n    const slide = swiper.virtual && swiper.params.virtual.enabled ? swiper.slidesEl.querySelector(`[data-swiper-slide-index=\"${index}\"]`) : swiper.slides[index];\n    let value = slugify(slide.getAttribute('data-history'));\n    if (swiper.params.history.root.length > 0) {\n      let root = swiper.params.history.root;\n      if (root[root.length - 1] === '/') root = root.slice(0, root.length - 1);\n      value = `${root}/${key ? `${key}/` : ''}${value}`;\n    } else if (!location.pathname.includes(key)) {\n      value = `${key ? `${key}/` : ''}${value}`;\n    }\n    if (swiper.params.history.keepQuery) {\n      value += location.search;\n    }\n    const currentState = window.history.state;\n    if (currentState && currentState.value === value) {\n      return;\n    }\n    if (swiper.params.history.replaceState) {\n      window.history.replaceState({\n        value\n      }, null, value);\n    } else {\n      window.history.pushState({\n        value\n      }, null, value);\n    }\n  };\n  const scrollToSlide = (speed, value, runCallbacks) => {\n    if (value) {\n      for (let i = 0, length = swiper.slides.length; i < length; i += 1) {\n        const slide = swiper.slides[i];\n        const slideHistory = slugify(slide.getAttribute('data-history'));\n        if (slideHistory === value) {\n          const index = swiper.getSlideIndex(slide);\n          swiper.slideTo(index, speed, runCallbacks);\n        }\n      }\n    } else {\n      swiper.slideTo(0, speed, runCallbacks);\n    }\n  };\n  const setHistoryPopState = () => {\n    paths = getPathValues(swiper.params.url);\n    scrollToSlide(swiper.params.speed, paths.value, false);\n  };\n  const init = () => {\n    const window = getWindow();\n    if (!swiper.params.history) return;\n    if (!window.history || !window.history.pushState) {\n      swiper.params.history.enabled = false;\n      swiper.params.hashNavigation.enabled = true;\n      return;\n    }\n    initialized = true;\n    paths = getPathValues(swiper.params.url);\n    if (!paths.key && !paths.value) {\n      if (!swiper.params.history.replaceState) {\n        window.addEventListener('popstate', setHistoryPopState);\n      }\n      return;\n    }\n    scrollToSlide(0, paths.value, swiper.params.runCallbacksOnInit);\n    if (!swiper.params.history.replaceState) {\n      window.addEventListener('popstate', setHistoryPopState);\n    }\n  };\n  const destroy = () => {\n    const window = getWindow();\n    if (!swiper.params.history.replaceState) {\n      window.removeEventListener('popstate', setHistoryPopState);\n    }\n  };\n  on('init', () => {\n    if (swiper.params.history.enabled) {\n      init();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.params.history.enabled) {\n      destroy();\n    }\n  });\n  on('transitionEnd _freeModeNoMomentumRelease', () => {\n    if (initialized) {\n      setHistory(swiper.params.history.key, swiper.activeIndex);\n    }\n  });\n  on('slideChange', () => {\n    if (initialized && swiper.params.cssMode) {\n      setHistory(swiper.params.history.key, swiper.activeIndex);\n    }\n  });\n}\nexport { History as default };", "map": {"version": 3, "names": ["a", "getWindow", "History", "_ref", "swiper", "extendParams", "on", "history", "enabled", "root", "replaceState", "key", "<PERSON><PERSON><PERSON><PERSON>", "initialized", "paths", "slugify", "text", "toString", "replace", "get<PERSON>ath<PERSON><PERSON><PERSON>", "urlOverride", "window", "location", "URL", "pathArray", "pathname", "slice", "split", "filter", "part", "total", "length", "value", "setHistory", "index", "params", "url", "slide", "virtual", "slidesEl", "querySelector", "slides", "getAttribute", "includes", "search", "currentState", "state", "pushState", "scrollToSlide", "speed", "runCallbacks", "i", "slideHistory", "getSlideIndex", "slideTo", "setHistoryPopState", "init", "hashNavigation", "addEventListener", "runCallbacksOnInit", "destroy", "removeEventListener", "activeIndex", "cssMode", "default"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/node_modules/swiper/modules/history.mjs"], "sourcesContent": ["import { a as getWindow } from '../shared/ssr-window.esm.mjs';\n\nfunction History(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    history: {\n      enabled: false,\n      root: '',\n      replaceState: false,\n      key: 'slides',\n      keepQuery: false\n    }\n  });\n  let initialized = false;\n  let paths = {};\n  const slugify = text => {\n    return text.toString().replace(/\\s+/g, '-').replace(/[^\\w-]+/g, '').replace(/--+/g, '-').replace(/^-+/, '').replace(/-+$/, '');\n  };\n  const getPathValues = urlOverride => {\n    const window = getWindow();\n    let location;\n    if (urlOverride) {\n      location = new URL(urlOverride);\n    } else {\n      location = window.location;\n    }\n    const pathArray = location.pathname.slice(1).split('/').filter(part => part !== '');\n    const total = pathArray.length;\n    const key = pathArray[total - 2];\n    const value = pathArray[total - 1];\n    return {\n      key,\n      value\n    };\n  };\n  const setHistory = (key, index) => {\n    const window = getWindow();\n    if (!initialized || !swiper.params.history.enabled) return;\n    let location;\n    if (swiper.params.url) {\n      location = new URL(swiper.params.url);\n    } else {\n      location = window.location;\n    }\n    const slide = swiper.virtual && swiper.params.virtual.enabled ? swiper.slidesEl.querySelector(`[data-swiper-slide-index=\"${index}\"]`) : swiper.slides[index];\n    let value = slugify(slide.getAttribute('data-history'));\n    if (swiper.params.history.root.length > 0) {\n      let root = swiper.params.history.root;\n      if (root[root.length - 1] === '/') root = root.slice(0, root.length - 1);\n      value = `${root}/${key ? `${key}/` : ''}${value}`;\n    } else if (!location.pathname.includes(key)) {\n      value = `${key ? `${key}/` : ''}${value}`;\n    }\n    if (swiper.params.history.keepQuery) {\n      value += location.search;\n    }\n    const currentState = window.history.state;\n    if (currentState && currentState.value === value) {\n      return;\n    }\n    if (swiper.params.history.replaceState) {\n      window.history.replaceState({\n        value\n      }, null, value);\n    } else {\n      window.history.pushState({\n        value\n      }, null, value);\n    }\n  };\n  const scrollToSlide = (speed, value, runCallbacks) => {\n    if (value) {\n      for (let i = 0, length = swiper.slides.length; i < length; i += 1) {\n        const slide = swiper.slides[i];\n        const slideHistory = slugify(slide.getAttribute('data-history'));\n        if (slideHistory === value) {\n          const index = swiper.getSlideIndex(slide);\n          swiper.slideTo(index, speed, runCallbacks);\n        }\n      }\n    } else {\n      swiper.slideTo(0, speed, runCallbacks);\n    }\n  };\n  const setHistoryPopState = () => {\n    paths = getPathValues(swiper.params.url);\n    scrollToSlide(swiper.params.speed, paths.value, false);\n  };\n  const init = () => {\n    const window = getWindow();\n    if (!swiper.params.history) return;\n    if (!window.history || !window.history.pushState) {\n      swiper.params.history.enabled = false;\n      swiper.params.hashNavigation.enabled = true;\n      return;\n    }\n    initialized = true;\n    paths = getPathValues(swiper.params.url);\n    if (!paths.key && !paths.value) {\n      if (!swiper.params.history.replaceState) {\n        window.addEventListener('popstate', setHistoryPopState);\n      }\n      return;\n    }\n    scrollToSlide(0, paths.value, swiper.params.runCallbacksOnInit);\n    if (!swiper.params.history.replaceState) {\n      window.addEventListener('popstate', setHistoryPopState);\n    }\n  };\n  const destroy = () => {\n    const window = getWindow();\n    if (!swiper.params.history.replaceState) {\n      window.removeEventListener('popstate', setHistoryPopState);\n    }\n  };\n  on('init', () => {\n    if (swiper.params.history.enabled) {\n      init();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.params.history.enabled) {\n      destroy();\n    }\n  });\n  on('transitionEnd _freeModeNoMomentumRelease', () => {\n    if (initialized) {\n      setHistory(swiper.params.history.key, swiper.activeIndex);\n    }\n  });\n  on('slideChange', () => {\n    if (initialized && swiper.params.cssMode) {\n      setHistory(swiper.params.history.key, swiper.activeIndex);\n    }\n  });\n}\n\nexport { History as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,SAAS,QAAQ,8BAA8B;AAE7D,SAASC,OAAOA,CAACC,IAAI,EAAE;EACrB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAGH,IAAI;EACRE,YAAY,CAAC;IACXE,OAAO,EAAE;MACPC,OAAO,EAAE,KAAK;MACdC,IAAI,EAAE,EAAE;MACRC,YAAY,EAAE,KAAK;MACnBC,GAAG,EAAE,QAAQ;MACbC,SAAS,EAAE;IACb;EACF,CAAC,CAAC;EACF,IAAIC,WAAW,GAAG,KAAK;EACvB,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,MAAMC,OAAO,GAAGC,IAAI,IAAI;IACtB,OAAOA,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAChI,CAAC;EACD,MAAMC,aAAa,GAAGC,WAAW,IAAI;IACnC,MAAMC,MAAM,GAAGpB,SAAS,CAAC,CAAC;IAC1B,IAAIqB,QAAQ;IACZ,IAAIF,WAAW,EAAE;MACfE,QAAQ,GAAG,IAAIC,GAAG,CAACH,WAAW,CAAC;IACjC,CAAC,MAAM;MACLE,QAAQ,GAAGD,MAAM,CAACC,QAAQ;IAC5B;IACA,MAAME,SAAS,GAAGF,QAAQ,CAACG,QAAQ,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAK,EAAE,CAAC;IACnF,MAAMC,KAAK,GAAGN,SAAS,CAACO,MAAM;IAC9B,MAAMpB,GAAG,GAAGa,SAAS,CAACM,KAAK,GAAG,CAAC,CAAC;IAChC,MAAME,KAAK,GAAGR,SAAS,CAACM,KAAK,GAAG,CAAC,CAAC;IAClC,OAAO;MACLnB,GAAG;MACHqB;IACF,CAAC;EACH,CAAC;EACD,MAAMC,UAAU,GAAGA,CAACtB,GAAG,EAAEuB,KAAK,KAAK;IACjC,MAAMb,MAAM,GAAGpB,SAAS,CAAC,CAAC;IAC1B,IAAI,CAACY,WAAW,IAAI,CAACT,MAAM,CAAC+B,MAAM,CAAC5B,OAAO,CAACC,OAAO,EAAE;IACpD,IAAIc,QAAQ;IACZ,IAAIlB,MAAM,CAAC+B,MAAM,CAACC,GAAG,EAAE;MACrBd,QAAQ,GAAG,IAAIC,GAAG,CAACnB,MAAM,CAAC+B,MAAM,CAACC,GAAG,CAAC;IACvC,CAAC,MAAM;MACLd,QAAQ,GAAGD,MAAM,CAACC,QAAQ;IAC5B;IACA,MAAMe,KAAK,GAAGjC,MAAM,CAACkC,OAAO,IAAIlC,MAAM,CAAC+B,MAAM,CAACG,OAAO,CAAC9B,OAAO,GAAGJ,MAAM,CAACmC,QAAQ,CAACC,aAAa,CAAC,6BAA6BN,KAAK,IAAI,CAAC,GAAG9B,MAAM,CAACqC,MAAM,CAACP,KAAK,CAAC;IAC5J,IAAIF,KAAK,GAAGjB,OAAO,CAACsB,KAAK,CAACK,YAAY,CAAC,cAAc,CAAC,CAAC;IACvD,IAAItC,MAAM,CAAC+B,MAAM,CAAC5B,OAAO,CAACE,IAAI,CAACsB,MAAM,GAAG,CAAC,EAAE;MACzC,IAAItB,IAAI,GAAGL,MAAM,CAAC+B,MAAM,CAAC5B,OAAO,CAACE,IAAI;MACrC,IAAIA,IAAI,CAACA,IAAI,CAACsB,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAEtB,IAAI,GAAGA,IAAI,CAACiB,KAAK,CAAC,CAAC,EAAEjB,IAAI,CAACsB,MAAM,GAAG,CAAC,CAAC;MACxEC,KAAK,GAAG,GAAGvB,IAAI,IAAIE,GAAG,GAAG,GAAGA,GAAG,GAAG,GAAG,EAAE,GAAGqB,KAAK,EAAE;IACnD,CAAC,MAAM,IAAI,CAACV,QAAQ,CAACG,QAAQ,CAACkB,QAAQ,CAAChC,GAAG,CAAC,EAAE;MAC3CqB,KAAK,GAAG,GAAGrB,GAAG,GAAG,GAAGA,GAAG,GAAG,GAAG,EAAE,GAAGqB,KAAK,EAAE;IAC3C;IACA,IAAI5B,MAAM,CAAC+B,MAAM,CAAC5B,OAAO,CAACK,SAAS,EAAE;MACnCoB,KAAK,IAAIV,QAAQ,CAACsB,MAAM;IAC1B;IACA,MAAMC,YAAY,GAAGxB,MAAM,CAACd,OAAO,CAACuC,KAAK;IACzC,IAAID,YAAY,IAAIA,YAAY,CAACb,KAAK,KAAKA,KAAK,EAAE;MAChD;IACF;IACA,IAAI5B,MAAM,CAAC+B,MAAM,CAAC5B,OAAO,CAACG,YAAY,EAAE;MACtCW,MAAM,CAACd,OAAO,CAACG,YAAY,CAAC;QAC1BsB;MACF,CAAC,EAAE,IAAI,EAAEA,KAAK,CAAC;IACjB,CAAC,MAAM;MACLX,MAAM,CAACd,OAAO,CAACwC,SAAS,CAAC;QACvBf;MACF,CAAC,EAAE,IAAI,EAAEA,KAAK,CAAC;IACjB;EACF,CAAC;EACD,MAAMgB,aAAa,GAAGA,CAACC,KAAK,EAAEjB,KAAK,EAAEkB,YAAY,KAAK;IACpD,IAAIlB,KAAK,EAAE;MACT,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEpB,MAAM,GAAG3B,MAAM,CAACqC,MAAM,CAACV,MAAM,EAAEoB,CAAC,GAAGpB,MAAM,EAAEoB,CAAC,IAAI,CAAC,EAAE;QACjE,MAAMd,KAAK,GAAGjC,MAAM,CAACqC,MAAM,CAACU,CAAC,CAAC;QAC9B,MAAMC,YAAY,GAAGrC,OAAO,CAACsB,KAAK,CAACK,YAAY,CAAC,cAAc,CAAC,CAAC;QAChE,IAAIU,YAAY,KAAKpB,KAAK,EAAE;UAC1B,MAAME,KAAK,GAAG9B,MAAM,CAACiD,aAAa,CAAChB,KAAK,CAAC;UACzCjC,MAAM,CAACkD,OAAO,CAACpB,KAAK,EAAEe,KAAK,EAAEC,YAAY,CAAC;QAC5C;MACF;IACF,CAAC,MAAM;MACL9C,MAAM,CAACkD,OAAO,CAAC,CAAC,EAAEL,KAAK,EAAEC,YAAY,CAAC;IACxC;EACF,CAAC;EACD,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAC/BzC,KAAK,GAAGK,aAAa,CAACf,MAAM,CAAC+B,MAAM,CAACC,GAAG,CAAC;IACxCY,aAAa,CAAC5C,MAAM,CAAC+B,MAAM,CAACc,KAAK,EAAEnC,KAAK,CAACkB,KAAK,EAAE,KAAK,CAAC;EACxD,CAAC;EACD,MAAMwB,IAAI,GAAGA,CAAA,KAAM;IACjB,MAAMnC,MAAM,GAAGpB,SAAS,CAAC,CAAC;IAC1B,IAAI,CAACG,MAAM,CAAC+B,MAAM,CAAC5B,OAAO,EAAE;IAC5B,IAAI,CAACc,MAAM,CAACd,OAAO,IAAI,CAACc,MAAM,CAACd,OAAO,CAACwC,SAAS,EAAE;MAChD3C,MAAM,CAAC+B,MAAM,CAAC5B,OAAO,CAACC,OAAO,GAAG,KAAK;MACrCJ,MAAM,CAAC+B,MAAM,CAACsB,cAAc,CAACjD,OAAO,GAAG,IAAI;MAC3C;IACF;IACAK,WAAW,GAAG,IAAI;IAClBC,KAAK,GAAGK,aAAa,CAACf,MAAM,CAAC+B,MAAM,CAACC,GAAG,CAAC;IACxC,IAAI,CAACtB,KAAK,CAACH,GAAG,IAAI,CAACG,KAAK,CAACkB,KAAK,EAAE;MAC9B,IAAI,CAAC5B,MAAM,CAAC+B,MAAM,CAAC5B,OAAO,CAACG,YAAY,EAAE;QACvCW,MAAM,CAACqC,gBAAgB,CAAC,UAAU,EAAEH,kBAAkB,CAAC;MACzD;MACA;IACF;IACAP,aAAa,CAAC,CAAC,EAAElC,KAAK,CAACkB,KAAK,EAAE5B,MAAM,CAAC+B,MAAM,CAACwB,kBAAkB,CAAC;IAC/D,IAAI,CAACvD,MAAM,CAAC+B,MAAM,CAAC5B,OAAO,CAACG,YAAY,EAAE;MACvCW,MAAM,CAACqC,gBAAgB,CAAC,UAAU,EAAEH,kBAAkB,CAAC;IACzD;EACF,CAAC;EACD,MAAMK,OAAO,GAAGA,CAAA,KAAM;IACpB,MAAMvC,MAAM,GAAGpB,SAAS,CAAC,CAAC;IAC1B,IAAI,CAACG,MAAM,CAAC+B,MAAM,CAAC5B,OAAO,CAACG,YAAY,EAAE;MACvCW,MAAM,CAACwC,mBAAmB,CAAC,UAAU,EAAEN,kBAAkB,CAAC;IAC5D;EACF,CAAC;EACDjD,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAIF,MAAM,CAAC+B,MAAM,CAAC5B,OAAO,CAACC,OAAO,EAAE;MACjCgD,IAAI,CAAC,CAAC;IACR;EACF,CAAC,CAAC;EACFlD,EAAE,CAAC,SAAS,EAAE,MAAM;IAClB,IAAIF,MAAM,CAAC+B,MAAM,CAAC5B,OAAO,CAACC,OAAO,EAAE;MACjCoD,OAAO,CAAC,CAAC;IACX;EACF,CAAC,CAAC;EACFtD,EAAE,CAAC,0CAA0C,EAAE,MAAM;IACnD,IAAIO,WAAW,EAAE;MACfoB,UAAU,CAAC7B,MAAM,CAAC+B,MAAM,CAAC5B,OAAO,CAACI,GAAG,EAAEP,MAAM,CAAC0D,WAAW,CAAC;IAC3D;EACF,CAAC,CAAC;EACFxD,EAAE,CAAC,aAAa,EAAE,MAAM;IACtB,IAAIO,WAAW,IAAIT,MAAM,CAAC+B,MAAM,CAAC4B,OAAO,EAAE;MACxC9B,UAAU,CAAC7B,MAAM,CAAC+B,MAAM,CAAC5B,OAAO,CAACI,GAAG,EAAEP,MAAM,CAAC0D,WAAW,CAAC;IAC3D;EACF,CAAC,CAAC;AACJ;AAEA,SAAS5D,OAAO,IAAI8D,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}