{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\portfolio-react\\\\src\\\\components\\\\Intro.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Intro = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"intro\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: [\"Welcome, I'M\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 6,\n        columnNumber: 23\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"highlight\",\n        children: \"Chouchane Med Amine\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 6,\n        columnNumber: 29\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Software Developer\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Intro;\nexport default Intro;\nvar _c;\n$RefreshReg$(_c, \"Intro\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Intro", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/components/Intro.js"], "sourcesContent": ["import React from 'react';\n\nconst Intro = () => {\n  return (\n    <section className=\"intro\">\n      <h1>Welcome, I'M<br /><span className=\"highlight\">Chouchane Med Amine</span></h1>\n      <p>Software Developer</p>\n    </section>\n  );\n};\n\nexport default Intro;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAClB,oBACED,OAAA;IAASE,SAAS,EAAC,OAAO;IAAAC,QAAA,gBACxBH,OAAA;MAAAG,QAAA,GAAI,cAAY,eAAAH,OAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAAAP,OAAA;QAAME,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACjFP,OAAA;MAAAG,QAAA,EAAG;IAAkB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClB,CAAC;AAEd,CAAC;AAACC,EAAA,GAPIP,KAAK;AASX,eAAeA,KAAK;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}