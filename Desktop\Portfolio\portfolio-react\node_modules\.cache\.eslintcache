[{"C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\JobDetail.js": "4", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Home.js": "5", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Header.js": "6", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Footer.js": "7", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\ProjectImageSwiper.js": "8", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\NDANotification.js": "9", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\IntroCrafting.js": "10", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Statistics.js": "11", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\SkillsTicker.js": "12", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Experience.js": "13", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Portfolio.js": "14", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\ClientThoughts.js": "15", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Contact.js": "16", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Intro.js": "17", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\data\\jobsData.js": "18"}, {"size": 535, "mtime": 1750766591000, "results": "19", "hashOfConfig": "20"}, {"size": 478, "mtime": 1750766591000, "results": "21", "hashOfConfig": "20"}, {"size": 362, "mtime": 1750766591000, "results": "22", "hashOfConfig": "20"}, {"size": 6549, "mtime": 1750766591000, "results": "23", "hashOfConfig": "20"}, {"size": 710, "mtime": 1750766591000, "results": "24", "hashOfConfig": "20"}, {"size": 632, "mtime": 1750766591000, "results": "25", "hashOfConfig": "20"}, {"size": 378, "mtime": 1750766591000, "results": "26", "hashOfConfig": "20"}, {"size": 6543, "mtime": 1750766591000, "results": "27", "hashOfConfig": "20"}, {"size": 1770, "mtime": 1750766591000, "results": "28", "hashOfConfig": "20"}, {"size": 3068, "mtime": 1750766591000, "results": "29", "hashOfConfig": "20"}, {"size": 786, "mtime": 1750766591000, "results": "30", "hashOfConfig": "20"}, {"size": 843, "mtime": 1750766591000, "results": "31", "hashOfConfig": "20"}, {"size": 1765, "mtime": 1750766591000, "results": "32", "hashOfConfig": "20"}, {"size": 5313, "mtime": 1750766591000, "results": "33", "hashOfConfig": "20"}, {"size": 750, "mtime": 1750766591000, "results": "34", "hashOfConfig": "20"}, {"size": 1289, "mtime": 1750766591000, "results": "35", "hashOfConfig": "20"}, {"size": 259, "mtime": 1750766591000, "results": "36", "hashOfConfig": "20"}, {"size": 13916, "mtime": 1750766591000, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "fii283", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\JobDetail.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\ProjectImageSwiper.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\NDANotification.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\IntroCrafting.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Statistics.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\SkillsTicker.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Experience.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Portfolio.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\ClientThoughts.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Contact.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Intro.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\data\\jobsData.js", [], []]