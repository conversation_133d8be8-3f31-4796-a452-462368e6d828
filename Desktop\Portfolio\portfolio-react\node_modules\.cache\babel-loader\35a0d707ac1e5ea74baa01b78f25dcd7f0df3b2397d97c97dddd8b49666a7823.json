{"ast": null, "code": "function effectInit(params) {\n  const {\n    effect,\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    overwriteParams,\n    perspective,\n    recreateShadows,\n    getEffectParams\n  } = params;\n  on('beforeInit', () => {\n    if (swiper.params.effect !== effect) return;\n    swiper.classNames.push(`${swiper.params.containerModifierClass}${effect}`);\n    if (perspective && perspective()) {\n      swiper.classNames.push(`${swiper.params.containerModifierClass}3d`);\n    }\n    const overwriteParamsResult = overwriteParams ? overwriteParams() : {};\n    Object.assign(swiper.params, overwriteParamsResult);\n    Object.assign(swiper.originalParams, overwriteParamsResult);\n  });\n  on('setTranslate _virtualUpdated', () => {\n    if (swiper.params.effect !== effect) return;\n    setTranslate();\n  });\n  on('setTransition', (_s, duration) => {\n    if (swiper.params.effect !== effect) return;\n    setTransition(duration);\n  });\n  on('transitionEnd', () => {\n    if (swiper.params.effect !== effect) return;\n    if (recreateShadows) {\n      if (!getEffectParams || !getEffectParams().slideShadows) return;\n      // remove shadows\n      swiper.slides.forEach(slideEl => {\n        slideEl.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => shadowEl.remove());\n      });\n      // create new one\n      recreateShadows();\n    }\n  });\n  let requireUpdateOnVirtual;\n  on('virtualUpdate', () => {\n    if (swiper.params.effect !== effect) return;\n    if (!swiper.slides.length) {\n      requireUpdateOnVirtual = true;\n    }\n    requestAnimationFrame(() => {\n      if (requireUpdateOnVirtual && swiper.slides && swiper.slides.length) {\n        setTranslate();\n        requireUpdateOnVirtual = false;\n      }\n    });\n  });\n}\nexport { effectInit as e };", "map": {"version": 3, "names": ["effectInit", "params", "effect", "swiper", "on", "setTranslate", "setTransition", "overwriteParams", "perspective", "recreateShadows", "getEffectParams", "classNames", "push", "containerModifierClass", "overwriteParamsResult", "Object", "assign", "originalParams", "_s", "duration", "slideShadows", "slides", "for<PERSON>ach", "slideEl", "querySelectorAll", "shadowEl", "remove", "requireUpdateOnVirtual", "length", "requestAnimationFrame", "e"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/node_modules/swiper/shared/effect-init.mjs"], "sourcesContent": ["function effectInit(params) {\n  const {\n    effect,\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    overwriteParams,\n    perspective,\n    recreateShadows,\n    getEffectParams\n  } = params;\n  on('beforeInit', () => {\n    if (swiper.params.effect !== effect) return;\n    swiper.classNames.push(`${swiper.params.containerModifierClass}${effect}`);\n    if (perspective && perspective()) {\n      swiper.classNames.push(`${swiper.params.containerModifierClass}3d`);\n    }\n    const overwriteParamsResult = overwriteParams ? overwriteParams() : {};\n    Object.assign(swiper.params, overwriteParamsResult);\n    Object.assign(swiper.originalParams, overwriteParamsResult);\n  });\n  on('setTranslate _virtualUpdated', () => {\n    if (swiper.params.effect !== effect) return;\n    setTranslate();\n  });\n  on('setTransition', (_s, duration) => {\n    if (swiper.params.effect !== effect) return;\n    setTransition(duration);\n  });\n  on('transitionEnd', () => {\n    if (swiper.params.effect !== effect) return;\n    if (recreateShadows) {\n      if (!getEffectParams || !getEffectParams().slideShadows) return;\n      // remove shadows\n      swiper.slides.forEach(slideEl => {\n        slideEl.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => shadowEl.remove());\n      });\n      // create new one\n      recreateShadows();\n    }\n  });\n  let requireUpdateOnVirtual;\n  on('virtualUpdate', () => {\n    if (swiper.params.effect !== effect) return;\n    if (!swiper.slides.length) {\n      requireUpdateOnVirtual = true;\n    }\n    requestAnimationFrame(() => {\n      if (requireUpdateOnVirtual && swiper.slides && swiper.slides.length) {\n        setTranslate();\n        requireUpdateOnVirtual = false;\n      }\n    });\n  });\n}\n\nexport { effectInit as e };\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,MAAM,EAAE;EAC1B,MAAM;IACJC,MAAM;IACNC,MAAM;IACNC,EAAE;IACFC,YAAY;IACZC,aAAa;IACbC,eAAe;IACfC,WAAW;IACXC,eAAe;IACfC;EACF,CAAC,GAAGT,MAAM;EACVG,EAAE,CAAC,YAAY,EAAE,MAAM;IACrB,IAAID,MAAM,CAACF,MAAM,CAACC,MAAM,KAAKA,MAAM,EAAE;IACrCC,MAAM,CAACQ,UAAU,CAACC,IAAI,CAAC,GAAGT,MAAM,CAACF,MAAM,CAACY,sBAAsB,GAAGX,MAAM,EAAE,CAAC;IAC1E,IAAIM,WAAW,IAAIA,WAAW,CAAC,CAAC,EAAE;MAChCL,MAAM,CAACQ,UAAU,CAACC,IAAI,CAAC,GAAGT,MAAM,CAACF,MAAM,CAACY,sBAAsB,IAAI,CAAC;IACrE;IACA,MAAMC,qBAAqB,GAAGP,eAAe,GAAGA,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;IACtEQ,MAAM,CAACC,MAAM,CAACb,MAAM,CAACF,MAAM,EAAEa,qBAAqB,CAAC;IACnDC,MAAM,CAACC,MAAM,CAACb,MAAM,CAACc,cAAc,EAAEH,qBAAqB,CAAC;EAC7D,CAAC,CAAC;EACFV,EAAE,CAAC,8BAA8B,EAAE,MAAM;IACvC,IAAID,MAAM,CAACF,MAAM,CAACC,MAAM,KAAKA,MAAM,EAAE;IACrCG,YAAY,CAAC,CAAC;EAChB,CAAC,CAAC;EACFD,EAAE,CAAC,eAAe,EAAE,CAACc,EAAE,EAAEC,QAAQ,KAAK;IACpC,IAAIhB,MAAM,CAACF,MAAM,CAACC,MAAM,KAAKA,MAAM,EAAE;IACrCI,aAAa,CAACa,QAAQ,CAAC;EACzB,CAAC,CAAC;EACFf,EAAE,CAAC,eAAe,EAAE,MAAM;IACxB,IAAID,MAAM,CAACF,MAAM,CAACC,MAAM,KAAKA,MAAM,EAAE;IACrC,IAAIO,eAAe,EAAE;MACnB,IAAI,CAACC,eAAe,IAAI,CAACA,eAAe,CAAC,CAAC,CAACU,YAAY,EAAE;MACzD;MACAjB,MAAM,CAACkB,MAAM,CAACC,OAAO,CAACC,OAAO,IAAI;QAC/BA,OAAO,CAACC,gBAAgB,CAAC,8GAA8G,CAAC,CAACF,OAAO,CAACG,QAAQ,IAAIA,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC;MACjL,CAAC,CAAC;MACF;MACAjB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,CAAC;EACF,IAAIkB,sBAAsB;EAC1BvB,EAAE,CAAC,eAAe,EAAE,MAAM;IACxB,IAAID,MAAM,CAACF,MAAM,CAACC,MAAM,KAAKA,MAAM,EAAE;IACrC,IAAI,CAACC,MAAM,CAACkB,MAAM,CAACO,MAAM,EAAE;MACzBD,sBAAsB,GAAG,IAAI;IAC/B;IACAE,qBAAqB,CAAC,MAAM;MAC1B,IAAIF,sBAAsB,IAAIxB,MAAM,CAACkB,MAAM,IAAIlB,MAAM,CAACkB,MAAM,CAACO,MAAM,EAAE;QACnEvB,YAAY,CAAC,CAAC;QACdsB,sBAAsB,GAAG,KAAK;MAChC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,SAAS3B,UAAU,IAAI8B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}