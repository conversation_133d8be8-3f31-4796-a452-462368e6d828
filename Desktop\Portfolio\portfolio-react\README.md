# Portfolio React Application

This is a modern React.js portfolio application for Chouchane Med Amine, converted from static HTML to a dynamic React application.

## Features

- **Dynamic Job Management**: No more separate HTML files for each job - all job data is managed through a centralized data structure
- **React Router**: Seamless navigation between main portfolio and job detail pages
- **Responsive Design**: Mobile-first design that works on all devices
- **Component-Based Architecture**: Modular, reusable components for easy maintenance
- **Modern Styling**: Glassmorphism effects, animations, and modern CSS

## Project Structure

```
src/
├── components/          # React components
│   ├── Header.js       # Navigation header
│   ├── Home.js         # Main portfolio page
│   ├── JobDetail.js    # Dynamic job detail page
│   ├── Experience.js   # Professional experience timeline
│   ├── Portfolio.js    # Project portfolio carousel
│   └── ...            # Other components
├── data/
│   └── jobsData.js     # Centralized job data
└── styles/             # CSS files
```

## Adding New Jobs

To add a new job experience, simply add a new object to the `jobsData` array in `src/data/jobsData.js`:

```javascript
{
  id: 5,
  slug: "new-job-slug",
  title: "Job Title",
  company: "Company Name",
  duration: "2024 - Present",
  // ... other job details
}
```

No need to create separate HTML files anymore!

## Available Scripts

### `npm start`
Runs the app in development mode at [http://localhost:3000](http://localhost:3000)

### `npm run build`
Builds the app for production to the `build` folder

### `npm test`
Launches the test runner

## Deployment

The built application can be deployed to any static hosting service like:
- Netlify
- Vercel
- GitHub Pages
- AWS S3

## Technologies Used

- React.js
- React Router
- Modern CSS with Flexbox/Grid
- Responsive Design
- Component-based Architecture

### Code Splitting

This section has moved here: [https://facebook.github.io/create-react-app/docs/code-splitting](https://facebook.github.io/create-react-app/docs/code-splitting)

### Analyzing the Bundle Size

This section has moved here: [https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size](https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size)

### Making a Progressive Web App

This section has moved here: [https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app](https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app)

### Advanced Configuration

This section has moved here: [https://facebook.github.io/create-react-app/docs/advanced-configuration](https://facebook.github.io/create-react-app/docs/advanced-configuration)

### Deployment

This section has moved here: [https://facebook.github.io/create-react-app/docs/deployment](https://facebook.github.io/create-react-app/docs/deployment)

### `npm run build` fails to minify

This section has moved here: [https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify](https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify)
